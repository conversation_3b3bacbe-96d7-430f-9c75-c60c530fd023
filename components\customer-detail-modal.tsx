"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, <PERSON>ertDialog<PERSON>oot<PERSON>, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import {
  User,
  Phone,
  Mail,
  MapPin,
  DollarSign,
  Calendar,
  MessageSquare,
  Bell,
  Plus,
  Loader2,
  X,
  CheckCircle,
  Clock,
  AlertTriangle,
  Edit,
  Trash2
} from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

// Utility function to format currency amounts properly
const formatCurrency = (amount: number | string): string => {
  // Convert to number and ensure it's valid
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(numAmount)) return '0'

  // Format with proper thousand separators and no unnecessary decimals
  return numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
}

interface CustomerData {
  id: string
  name: string
  phone: string
  email?: string
  address?: string
  loans: LoanRecord[]
  notes: ConversationNote[]
  reminders: Reminder[]
}

interface LoanRecord {
  id: string
  loan_amount: number
  amount_paid: number
  outstanding_amount?: number
  due_date: string
  due_date_bs?: string
  current_stage: string
  created_at: string
}

interface ConversationNote {
  id: string
  loan_id: string
  note_type: "general" | "call" | "email" | "meeting"
  content: string
  created_by_name: string
  created_at: string
}

interface Reminder {
  id: string
  loan_id: string
  title: string
  reminder_date: string
  reminder_date_bs?: string
  status: "pending" | "completed" | "cancelled"
  created_by_name: string
  created_at: string
}

interface CustomerDetailModalProps {
  customerId: string
  open: boolean
  onClose: () => void
  onUpdate: () => void
}

// Note form schema (loan_id will be added automatically)
const noteSchema = z.object({
  note_type: z.enum(["general", "call", "email", "meeting"]),
  content: z.string().min(1, "Content is required"),
})

// Reminder form schema (loan_id will be added automatically)
const reminderSchema = z.object({
  title: z.string().min(1, "Title is required"),
  reminder_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
})

// Edit customer form schema
const editCustomerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  address: z.string().optional(),
})

type NoteFormData = z.infer<typeof noteSchema> & { loan_id: string }
type ReminderFormData = z.infer<typeof reminderSchema> & { loan_id: string }
type EditCustomerFormData = z.infer<typeof editCustomerSchema>

// Fetch customer details
const fetchCustomerDetails = async (customerId: string): Promise<CustomerData> => {
  const response = await fetch(`/api/loan-recovery/customers/${customerId}`, {
    credentials: "include",
  })

  if (!response.ok) {
    throw new Error("Failed to fetch customer details")
  }

  const data = await response.json()
  return data.customer
}

export function CustomerDetailModal({ customerId, open, onClose, onUpdate }: CustomerDetailModalProps) {
  const [showAddNote, setShowAddNote] = useState(false)
  const [showAddReminder, setShowAddReminder] = useState(false)
  const [showEditCustomer, setShowEditCustomer] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const queryClient = useQueryClient()

  // Debug logging
  console.log('🔍 CustomerDetailModal props:', { customerId, open, hasOnClose: !!onClose })

  const { data: customer, isLoading, error } = useQuery({
    queryKey: ["customer", customerId],
    queryFn: () => fetchCustomerDetails(customerId),
    enabled: open && !!customerId,
  })

  const noteForm = useForm<NoteFormData>({
    resolver: zodResolver(noteSchema),
  })

  const reminderForm = useForm<ReminderFormData>({
    resolver: zodResolver(reminderSchema),
  })

  const editCustomerForm = useForm<EditCustomerFormData>({
    resolver: zodResolver(editCustomerSchema),
  })

  // Add note mutation
  const addNoteMutation = useMutation({
    mutationFn: async (data: NoteFormData) => {
      const response = await fetch("/api/loan-recovery/notes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(data),
      })
      if (!response.ok) throw new Error("Failed to add note")
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer", customerId] })
      noteForm.reset()
      setShowAddNote(false)
      toast({ title: "Success", description: "Note added successfully" })
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    },
  })

  // Add reminder mutation
  const addReminderMutation = useMutation({
    mutationFn: async (data: ReminderFormData) => {
      const response = await fetch("/api/loan-recovery/reminders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(data),
      })
      if (!response.ok) throw new Error("Failed to add reminder")
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer", customerId] })
      reminderForm.reset()
      setShowAddReminder(false)
      toast({ title: "Success", description: "Reminder added successfully" })
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    },
  })

  // Complete reminder mutation
  const completeReminderMutation = useMutation({
    mutationFn: async (reminderId: string) => {
      console.log('🔄 Completing reminder:', reminderId)
      const response = await fetch(`/api/loan-recovery/reminders/${reminderId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ status: "completed" }),
      })

      console.log('📡 Reminder completion response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ Reminder completion failed:', errorData)
        throw new Error(errorData.error || "Failed to complete reminder")
      }

      const result = await response.json()
      console.log('✅ Reminder completion successful:', result)
      return result
    },
    onSuccess: (data) => {
      console.log('🔄 Invalidating customer queries for:', customerId)
      queryClient.invalidateQueries({ queryKey: ["customer", customerId] })
      toast({ title: "Success", description: "Reminder marked as completed" })
    },
    onError: (error: Error) => {
      console.error('❌ Reminder completion mutation error:', error)
      toast({ title: "Error", description: error.message, variant: "destructive" })
    },
  })

  // Edit customer mutation
  const editCustomerMutation = useMutation({
    mutationFn: async (data: EditCustomerFormData) => {
      const response = await fetch(`/api/loan-recovery/customers/${customerId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(data),
      })
      if (!response.ok) throw new Error("Failed to update customer")
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer", customerId] })
      setShowEditCustomer(false)
      editCustomerForm.reset()
      onUpdate()
      toast({ title: "Success", description: "Customer updated successfully" })
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    },
  })

  // Delete customer mutation
  const deleteCustomerMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/loan-recovery/customers/${customerId}`, {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
      })
      if (!response.ok) throw new Error("Failed to delete customer")
      return response.json()
    },
    onSuccess: () => {
      setShowDeleteConfirm(false)
      onClose()
      onUpdate()
      toast({ title: "Success", description: "Customer deleted successfully" })
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    },
  })

  const handleAddNote = (data: Omit<NoteFormData, 'loan_id'>) => {
    // Use the first (primary) loan for the customer
    const primaryLoan = customer?.loans[0]
    if (!primaryLoan) {
      toast({ title: "Error", description: "No loan found for this customer", variant: "destructive" })
      return
    }

    const noteData: NoteFormData = {
      ...data,
      loan_id: primaryLoan.id
    }

    addNoteMutation.mutate(noteData)
  }

  const handleAddReminder = (data: Omit<ReminderFormData, 'loan_id'>) => {
    // Use the first (primary) loan for the customer
    const primaryLoan = customer?.loans[0]
    if (!primaryLoan) {
      toast({ title: "Error", description: "No loan found for this customer", variant: "destructive" })
      return
    }

    const reminderData: ReminderFormData = {
      ...data,
      loan_id: primaryLoan.id
    }

    addReminderMutation.mutate(reminderData)
  }

  const handleEditCustomer = (data: EditCustomerFormData) => {
    editCustomerMutation.mutate(data)
  }

  const handleDeleteCustomer = () => {
    deleteCustomerMutation.mutate()
  }

  const openEditForm = () => {
    if (customer) {
      editCustomerForm.reset({
        name: customer.name,
        phone: customer.phone,
        email: customer.email || "",
        address: customer.address || "",
      })
      setShowEditCustomer(true)
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case "early": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
      case "assertive": return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
      case "escalation": return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300"
      case "legal_recovery": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
      case "complete": return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getNoteTypeIcon = (type: string) => {
    switch (type) {
      case "call": return "📞"
      case "email": return "📧"
      case "meeting": return "🤝"
      default: return "💬"
    }
  }

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (error || !customer) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <div className="text-center p-8">
            <h3 className="text-lg font-semibold text-red-600 mb-2">Error</h3>
            <p className="text-gray-600">Failed to load customer details.</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  const totalLoanAmount = customer.loans.reduce((sum, loan) => sum + loan.loan_amount, 0)
  const totalPaid = customer.loans.reduce((sum, loan) => sum + loan.amount_paid, 0)
  const totalOutstanding = totalLoanAmount - totalPaid

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarFallback className="bg-exobank-green text-white text-lg">
                  {customer.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <DialogTitle className="text-xl">{customer.name}</DialogTitle>
                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {customer.phone}
                  </span>
                  {customer.email && (
                    <span className="flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      {customer.email}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-green-600">
                Rs. {formatCurrency(totalOutstanding)}
              </div>
              <div className="text-sm text-gray-500">
                Total: Rs. {formatCurrency(totalLoanAmount)} | Paid: Rs. {formatCurrency(totalPaid)}
              </div>
            </div>
          </div>
        </DialogHeader>

        {/* Two-column layout */}
        <div className="flex-1 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full max-h-[calc(90vh-200px)] overflow-y-auto p-1">
            {/* Left Column - Customer Information */}
            <div className="space-y-6">
              {/* Personal Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Personal Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Phone</Label>
                    <p className="text-sm mt-1 flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      {customer.phone}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</Label>
                    <p className="text-sm mt-1 flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      {customer.email || "Not provided"}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">Address</Label>
                    <p className="text-sm mt-1 flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      {customer.address || "Not provided"}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Loan Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Loan Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {customer.loans.map((loan) => (
                      <div key={loan.id} className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-semibold text-lg text-green-600 dark:text-green-400">
                              Rs. {formatCurrency(loan.outstanding_amount || (loan.loan_amount - loan.amount_paid))}
                            </div>
                            <div className="text-sm text-gray-500">
                              Total: Rs. {formatCurrency(loan.loan_amount)} | Paid: Rs. {formatCurrency(loan.amount_paid)}
                            </div>
                          </div>
                          <Badge className={getStageColor(loan.current_stage)}>
                            {loan.current_stage.replace("_", " ")}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Due: {loan.due_date_bs || loan.due_date}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Customer Management Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Customer Management
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-3">
                    <Button
                      onClick={openEditForm}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Edit Customer
                    </Button>

                    <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete Customer
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Customer</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete {customer.name}? This action cannot be undone and will also delete all associated loans, notes, and reminders.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleDeleteCustomer}
                            disabled={deleteCustomerMutation.isPending}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            {deleteCustomerMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin mr-1" />
                            ) : null}
                            Delete Customer
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Reminders and Notes */}
            <div className="space-y-6">
              {/* Reminders Section */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      Reminders
                      <Badge variant="secondary">{customer.reminders.filter(r => r.status === "pending").length}</Badge>
                    </CardTitle>
                    <Button
                      onClick={() => setShowAddReminder(true)}
                      size="sm"
                      className="bg-exobank-green hover:bg-exobank-green/90"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Reminder
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="max-h-64 overflow-y-auto">
                  {showAddReminder && (
                    <Card className="mb-4">
                      <CardContent className="p-4">
                        <form onSubmit={reminderForm.handleSubmit(handleAddReminder)} className="space-y-4">
                          <div className="grid grid-cols-1 gap-4">
                            <div>
                              <Label>Date</Label>
                              <Input
                                type="date"
                                {...reminderForm.register("reminder_date")}
                              />
                            </div>
                            <div>
                              <Label>Title</Label>
                              <Input
                                {...reminderForm.register("title")}
                                placeholder="Reminder title"
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              type="submit"
                              size="sm"
                              disabled={addReminderMutation.isPending}
                              className="bg-exobank-green hover:bg-exobank-green/90"
                            >
                              {addReminderMutation.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                "Add Reminder"
                              )}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setShowAddReminder(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </form>
                      </CardContent>
                    </Card>
                  )}

                  <div className="space-y-3">
                    {customer.reminders.length === 0 ? (
                      <p className="text-center text-gray-500 py-4">No reminders set</p>
                    ) : (
                      customer.reminders.map((reminder) => (
                        <div key={reminder.id} className="border rounded-lg p-3 bg-gray-50 dark:bg-gray-800">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {reminder.status === "completed" ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : reminder.status === "cancelled" ? (
                                <X className="h-4 w-4 text-red-600" />
                              ) : (
                                <Clock className="h-4 w-4 text-orange-600" />
                              )}
                              <span className="font-medium text-sm">{reminder.title}</span>
                            </div>
                            <Badge
                              variant={reminder.status === "completed" ? "default" : "secondary"}
                              className="text-xs"
                            >
                              {reminder.status}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              {reminder.reminder_date_bs || reminder.reminder_date}
                            </span>
                            {reminder.status === "pending" && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  console.log('🔘 Complete button clicked for reminder:', reminder.id)
                                  completeReminderMutation.mutate(reminder.id)
                                }}
                                disabled={completeReminderMutation.isPending}
                                className="text-xs h-6"
                              >
                                {completeReminderMutation.isPending ? (
                                  <Loader2 className="h-3 w-3 animate-spin" />
                                ) : (
                                  "Complete"
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Conversation Notes Section */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      Conversation Notes
                      <Badge variant="secondary">{customer.notes.length}</Badge>
                    </CardTitle>
                    <Button
                      onClick={() => setShowAddNote(true)}
                      size="sm"
                      className="bg-exobank-green hover:bg-exobank-green/90"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Note
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="max-h-64 overflow-y-auto">

                  {showAddNote && (
                    <Card className="mb-4">
                      <CardContent className="p-4">
                        <form onSubmit={noteForm.handleSubmit(handleAddNote)} className="space-y-4">
                          <div className="grid grid-cols-1 gap-4">
                            <div>
                              <Label>Type</Label>
                              <Select onValueChange={(value) => noteForm.setValue("note_type", value as any)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="general">💬 General</SelectItem>
                                  <SelectItem value="call">📞 Call</SelectItem>
                                  <SelectItem value="email">📧 Email</SelectItem>
                                  <SelectItem value="meeting">🤝 Meeting</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Content</Label>
                              <Textarea
                                {...noteForm.register("content")}
                                placeholder="Add a note about your conversation..."
                                rows={3}
                              />
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              type="submit"
                              size="sm"
                              disabled={addNoteMutation.isPending}
                              className="bg-exobank-green hover:bg-exobank-green/90"
                            >
                              {addNoteMutation.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                "Add Note"
                              )}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setShowAddNote(false)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </form>
                      </CardContent>
                    </Card>
                  )}

                  <div className="space-y-3">
                    {customer.notes.length === 0 ? (
                      <p className="text-center text-gray-500 py-4">No notes added yet</p>
                    ) : (
                      customer.notes.map((note) => (
                        <div key={note.id} className="border rounded-lg p-3 bg-gray-50 dark:bg-gray-800">
                          <div className="flex items-start gap-3">
                            <span className="text-lg">{getNoteTypeIcon(note.note_type)}</span>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {note.note_type}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {new Date(note.created_at).toLocaleDateString()} by {note.created_by_name}
                                </span>
                              </div>
                              <p className="text-sm">{note.content}</p>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Edit Customer Modal */}
        {showEditCustomer && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>Edit Customer</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={editCustomerForm.handleSubmit(handleEditCustomer)} className="space-y-4">
                  <div>
                    <Label>Name</Label>
                    <Input
                      {...editCustomerForm.register("name")}
                      placeholder="Customer name"
                    />
                    {editCustomerForm.formState.errors.name && (
                      <p className="text-sm text-red-600 mt-1">
                        {editCustomerForm.formState.errors.name.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label>Phone</Label>
                    <Input
                      {...editCustomerForm.register("phone")}
                      placeholder="Phone number"
                    />
                    {editCustomerForm.formState.errors.phone && (
                      <p className="text-sm text-red-600 mt-1">
                        {editCustomerForm.formState.errors.phone.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label>Email</Label>
                    <Input
                      {...editCustomerForm.register("email")}
                      placeholder="Email address (optional)"
                      type="email"
                    />
                    {editCustomerForm.formState.errors.email && (
                      <p className="text-sm text-red-600 mt-1">
                        {editCustomerForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label>Address</Label>
                    <Textarea
                      {...editCustomerForm.register("address")}
                      placeholder="Address (optional)"
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button
                      type="submit"
                      disabled={editCustomerMutation.isPending}
                      className="bg-exobank-green hover:bg-exobank-green/90"
                    >
                      {editCustomerMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : null}
                      Update Customer
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowEditCustomer(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

      </DialogContent>
    </Dialog>
  )
}
