"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Loader2, User, Phone, Mail, MapPin, DollarSign, Calendar } from "lucide-react"
import { toast } from "@/hooks/use-toast"
import { NepaliCalendar } from "@/lib/nepali-calendar"

// Form validation schema
const addCustomerSchema = z.object({
  // Personal Information
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  phone: z.string().min(1, "Phone is required").max(20, "Phone too long"),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  address: z.string().optional(),
  
  // Loan Details
  loan_amount: z.number().positive("Loan amount must be positive"),
  amount_paid: z.number().min(0, "Amount paid cannot be negative").default(0),
  due_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format"),
})

type AddCustomerFormData = z.infer<typeof addCustomerSchema>

interface AddCustomerModalProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function AddCustomerModal({ open, onClose, onSuccess }: AddCustomerModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [nepaliDate, setNepaliDate] = useState("")

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<AddCustomerFormData>({
    resolver: zodResolver(addCustomerSchema),
    defaultValues: {
      amount_paid: 0,
    },
  })

  const watchedDueDate = watch("due_date")

  // Convert AD date to BS when due_date changes
  const handleDateChange = (adDate: string) => {
    if (adDate) {
      try {
        const date = new Date(adDate)
        const bsDate = NepaliCalendar.adToBS(date)
        setNepaliDate(NepaliCalendar.formatBSDate(bsDate))
      } catch (error) {
        setNepaliDate("")
      }
    } else {
      setNepaliDate("")
    }
  }

  // Handle form submission
  const onSubmit = async (data: AddCustomerFormData) => {
    setIsSubmitting(true)
    
    try {
      // First create the customer
      const customerResponse = await fetch("/api/loan-recovery/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          name: data.name,
          phone: data.phone,
          email: data.email || null,
          address: data.address || null,
        }),
      })

      if (!customerResponse.ok) {
        const error = await customerResponse.json()
        throw new Error(error.error || "Failed to create customer")
      }

      const customerData = await customerResponse.json()
      const customerId = customerData.customer.id

      // Then create the loan record
      const loanResponse = await fetch("/api/loan-recovery/loans", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          customer_id: customerId,
          loan_amount: data.loan_amount,
          amount_paid: data.amount_paid,
          due_date: data.due_date,
          due_date_bs: nepaliDate,
        }),
      })

      if (!loanResponse.ok) {
        const error = await loanResponse.json()
        throw new Error(error.error || "Failed to create loan record")
      }

      toast({
        title: "Success",
        description: "Customer and loan record created successfully",
      })

      reset()
      setNepaliDate("")
      onSuccess()
    } catch (error) {
      console.error("Add customer error:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create customer",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    reset()
    setNepaliDate("")
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Add New Customer
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Personal Information Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <User className="h-4 w-4" />
              Personal Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name" className="flex items-center gap-1">
                  <span className="text-red-500">*</span>
                  Name
                </Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="Enter full name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="phone" className="flex items-center gap-1">
                  <Phone className="h-3 w-3" />
                  <span className="text-red-500">*</span>
                  Phone
                </Label>
                <Input
                  id="phone"
                  {...register("phone")}
                  placeholder="Enter phone number"
                  className={errors.phone ? "border-red-500" : ""}
                />
                {errors.phone && (
                  <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="email" className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  placeholder="Enter email address"
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="address" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  Address
                </Label>
                <Textarea
                  id="address"
                  {...register("address")}
                  placeholder="Enter address"
                  rows={3}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Loan Details Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Loan Details
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="loan_amount" className="flex items-center gap-1">
                  <span className="text-red-500">*</span>
                  Loan Amount (NPR)
                </Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    Rs.
                  </span>
                  <Input
                    id="loan_amount"
                    type="number"
                    step="0.01"
                    {...register("loan_amount", { valueAsNumber: true })}
                    placeholder="0.00"
                    className={`pl-10 ${errors.loan_amount ? "border-red-500" : ""}`}
                  />
                </div>
                {errors.loan_amount && (
                  <p className="text-red-500 text-sm mt-1">{errors.loan_amount.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="amount_paid">Amount Paid (NPR)</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    Rs.
                  </span>
                  <Input
                    id="amount_paid"
                    type="number"
                    step="0.01"
                    {...register("amount_paid", { valueAsNumber: true })}
                    placeholder="0.00"
                    className={`pl-10 ${errors.amount_paid ? "border-red-500" : ""}`}
                  />
                </div>
                {errors.amount_paid && (
                  <p className="text-red-500 text-sm mt-1">{errors.amount_paid.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="due_date" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span className="text-red-500">*</span>
                  Due Date
                </Label>
                <Input
                  id="due_date"
                  type="date"
                  {...register("due_date")}
                  onChange={(e) => {
                    setValue("due_date", e.target.value)
                    handleDateChange(e.target.value)
                  }}
                  className={errors.due_date ? "border-red-500" : ""}
                />
                {errors.due_date && (
                  <p className="text-red-500 text-sm mt-1">{errors.due_date.message}</p>
                )}
              </div>

              {nepaliDate && (
                <div>
                  <Label>Nepali Date (BS)</Label>
                  <Input
                    value={nepaliDate}
                    readOnly
                    className="bg-gray-50 dark:bg-gray-800"
                    placeholder="Auto-calculated"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-exobank-green hover:bg-exobank-green/90"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Add Customer"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
