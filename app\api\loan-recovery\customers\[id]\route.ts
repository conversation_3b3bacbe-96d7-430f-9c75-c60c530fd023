import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for customer updates
const updateCustomerSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  phone: z.string().min(1, "Phone is required").max(20, "Phone too long").optional(),
  email: z.string().email("Invalid email").optional(),
  address: z.string().optional(),
  employee_id: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const customerId = params.id

    // Get customer details with loan information
    const customerResult = await serverDb.sql`
      SELECT 
        c.*,
        u.full_name as created_by_name
      FROM loan_recovery_customers c
      LEFT JOIN users u ON c.created_by = u.id
      WHERE c.id = ${customerId}
    `

    if (customerResult.length === 0) {
      return NextResponse.json({ error: "Customer not found" }, { status: 404 })
    }

    const customer = customerResult[0]

    // Get loan records for this customer
    const loans = await serverDb.sql`
      SELECT 
        lr.*,
        u.full_name as created_by_name
      FROM loan_records lr
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.customer_id = ${customerId}
      ORDER BY lr.created_at DESC
    `

    // Get conversation notes
    const notes = await serverDb.sql`
      SELECT 
        lcn.*,
        u.full_name as created_by_name
      FROM loan_conversation_notes lcn
      LEFT JOIN users u ON lcn.created_by = u.id
      WHERE lcn.loan_id IN (
        SELECT id FROM loan_records WHERE customer_id = ${customerId}
      )
      ORDER BY lcn.created_at DESC
    `

    // Get reminders
    const reminders = await serverDb.sql`
      SELECT 
        lr.*,
        u.full_name as created_by_name
      FROM loan_reminders lr
      LEFT JOIN users u ON lr.created_by = u.id
      WHERE lr.loan_id IN (
        SELECT id FROM loan_records WHERE customer_id = ${customerId}
      )
      ORDER BY lr.reminder_date ASC
    `

    return NextResponse.json({
      success: true,
      customer: {
        ...customer,
        loans,
        notes,
        reminders,
      },
    })
  } catch (error) {
    console.error("Get customer API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const customerId = params.id
    const body = await request.json()
    const validatedData = updateCustomerSchema.parse(body)

    // Check if customer exists
    const existingCustomer = await serverDb.sql`
      SELECT id FROM loan_recovery_customers WHERE id = ${customerId}
    `

    if (existingCustomer.length === 0) {
      return NextResponse.json({ error: "Customer not found" }, { status: 404 })
    }

    // Check if phone number is being changed and if it conflicts
    if (validatedData.phone) {
      const phoneConflict = await serverDb.sql`
        SELECT id FROM loan_recovery_customers 
        WHERE phone = ${validatedData.phone} AND id != ${customerId}
      `

      if (phoneConflict.length > 0) {
        return NextResponse.json(
          { error: "Customer with this phone number already exists" },
          { status: 400 }
        )
      }
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []

    if (validatedData.name !== undefined) {
      updateFields.push("name = $" + (updateValues.length + 1))
      updateValues.push(validatedData.name)
    }
    if (validatedData.phone !== undefined) {
      updateFields.push("phone = $" + (updateValues.length + 1))
      updateValues.push(validatedData.phone)
    }
    if (validatedData.email !== undefined) {
      updateFields.push("email = $" + (updateValues.length + 1))
      updateValues.push(validatedData.email)
    }
    if (validatedData.address !== undefined) {
      updateFields.push("address = $" + (updateValues.length + 1))
      updateValues.push(validatedData.address)
    }
    if (validatedData.employee_id !== undefined) {
      updateFields.push("employee_id = $" + (updateValues.length + 1))
      updateValues.push(validatedData.employee_id)
    }

    updateFields.push("updated_at = NOW()")

    if (updateFields.length === 1) { // Only updated_at
      return NextResponse.json({ error: "No fields to update" }, { status: 400 })
    }

    // Build the update query manually since we have dynamic fields
    let query = `UPDATE loan_recovery_customers SET `
    const queryParams = []

    for (let i = 0; i < updateFields.length; i++) {
      if (i > 0) query += ", "
      query += updateFields[i]
      if (!updateFields[i].includes("NOW()")) {
        queryParams.push(updateValues[i])
      }
    }

    query += ` WHERE id = $${queryParams.length + 1} RETURNING *`
    queryParams.push(customerId)

    const customer = await serverDb.sql.unsafe(query, queryParams)

    return NextResponse.json({
      success: true,
      customer: customer[0],
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Update customer API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user || user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const customerId = params.id

    // Check if customer exists
    const existingCustomer = await serverDb.sql`
      SELECT id FROM loan_recovery_customers WHERE id = ${customerId}
    `

    if (existingCustomer.length === 0) {
      return NextResponse.json({ error: "Customer not found" }, { status: 404 })
    }

    // Delete customer (cascade will handle related records)
    await serverDb.sql`
      DELETE FROM loan_recovery_customers WHERE id = ${customerId}
    `

    return NextResponse.json({
      success: true,
      message: "Customer deleted successfully",
    })
  } catch (error) {
    console.error("Delete customer API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
