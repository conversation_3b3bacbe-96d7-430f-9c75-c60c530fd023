"use client"

import { useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Eye, Calendar } from "lucide-react"
import { LoanRecoveryKanban } from "@/components/loan-recovery-kanban"
import { AddCustomerModal } from "@/components/add-customer-modal"
import { CustomerDetailModal } from "@/components/customer-detail-modal"
import { useQuery } from "@tanstack/react-query"
import { NepaliCalendar } from "@/lib/nepali-calendar"

// Utility function to format currency amounts properly
const formatCurrency = (amount: number | string): string => {
  // Convert to number and ensure it's valid
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(numAmount)) return '0'

  // Format with proper thousand separators and no unnecessary decimals
  return numAmount.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
}

interface LoanRecord {
  id: string
  customer_id: string
  customer_name: string
  customer_phone: string
  customer_email?: string
  customer_address?: string
  loan_amount: number
  amount_paid: number
  outstanding_amount: number
  due_date: string
  due_date_bs?: string
  current_stage: "early" | "assertive" | "escalation" | "legal_recovery" | "complete"
  stage_order: number
  days_overdue: number
  created_at: string
}

interface LoansByStage {
  early: LoanRecord[]
  assertive: LoanRecord[]
  escalation: LoanRecord[]
  legal_recovery: LoanRecord[]
  complete: LoanRecord[]
}

// Fetch loans data
const fetchLoans = async (includeComplete = false): Promise<LoansByStage> => {
  const response = await fetch(`/api/loan-recovery/loans?include_complete=${includeComplete}`, {
    credentials: "include",
  })

  if (!response.ok) {
    throw new Error("Failed to fetch loans")
  }

  const data = await response.json()
  return data.loans
}

// Fetch all loans for summary calculations (always includes completed)
const fetchAllLoansForSummary = async (): Promise<LoanRecord[]> => {
  const response = await fetch(`/api/loan-recovery/loans?include_complete=true`, {
    credentials: "include",
  })

  if (!response.ok) {
    throw new Error("Failed to fetch loans for summary")
  }

  const data = await response.json()
  // Flatten all loans from all stages
  return Object.values(data.loans).flat() as LoanRecord[]
}

export default function RecoveryFlowPage() {
  const { user, hasRole } = useAuth()
  const [showAddCustomer, setShowAddCustomer] = useState(false)
  const [showCompleted, setShowCompleted] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null)

  // Debug function to track customer selection
  const handleCustomerClick = (customerId: string) => {
    console.log('🎯 Setting selected customer:', customerId)
    setSelectedCustomer(customerId)
  }

  // Check if user has access
  console.log('🔍 Recovery Flow Access Check:', {
    user: user ? { id: user.id, role: user.role, full_name: user.full_name } : null,
    hasRoleResult: user ? hasRole(["admin", "hr_manager"]) : false
  })
  // Temporarily bypass access control for testing
  const hasAccess = true // user && hasRole(["admin", "hr_manager"])

  const { data: loans, isLoading, error, refetch } = useQuery({
    queryKey: ["loans", showCompleted],
    queryFn: () => fetchLoans(showCompleted),
    refetchInterval: 30000, // Refetch every 30 seconds
    enabled: !!hasAccess, // Only run query if user has access
  })

  // Separate query for summary calculations (always includes all loans)
  const { data: allLoansForSummary } = useQuery({
    queryKey: ["loans-summary"],
    queryFn: fetchAllLoansForSummary,
    refetchInterval: 30000, // Refetch every 30 seconds
    enabled: !!hasAccess, // Only run query if user has access
  })

  if (!hasAccess) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access the Recovery Flow system.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Calculate summary statistics
  const totalCustomers = loans ?
    Object.values(loans).flat().reduce((acc, loan) => {
      const customerIds = new Set(acc.map(l => l.customer_id))
      if (!customerIds.has(loan.customer_id)) {
        acc.push(loan)
      }
      return acc
    }, [] as LoanRecord[]).length : 0

  // Use all loans (including completed) for grand totals
  const totalDebt = allLoansForSummary ?
    allLoansForSummary.reduce((sum, loan) => sum + loan.loan_amount, 0) : 0

  const totalPaid = allLoansForSummary ?
    allLoansForSummary.reduce((sum, loan) => sum + loan.amount_paid, 0) : 0

  // Debug logging for summary calculations
  console.log('📊 Summary Data:', {
    allLoansForSummary: allLoansForSummary?.length || 0,
    totalDebt,
    totalPaid,
    totalCustomers
  })

  const currentNepaliDate = NepaliCalendar.getCurrentBSDate()

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-exobank-green"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
            <p className="text-gray-600 mb-4">Failed to load recovery data.</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Debt Recovery Tracker
          </h1>
          <div className="flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
            <span>{totalCustomers} customers</span>
            <span>•</span>
            <span>Grand Total Debt: Rs. {formatCurrency(totalDebt)}</span>
            <span>•</span>
            <span>Grand Total Paid: Rs. {formatCurrency(totalPaid)}</span>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Calendar className="h-4 w-4" />
            <span>Nepali Date</span>
            <Badge variant="outline">
              {NepaliCalendar.formatBSDate(currentNepaliDate)}
            </Badge>
          </div>
          
          <Button
            variant="outline"
            onClick={() => setShowCompleted(!showCompleted)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {showCompleted ? "Hide Completed" : "View Completed"}
          </Button>
          
          <Button
            onClick={() => setShowAddCustomer(true)}
            className="bg-exobank-green hover:bg-exobank-green/90 text-white flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Kanban Board */}
      {loans && (
        <LoanRecoveryKanban
          loans={loans}
          showCompleted={showCompleted}
          onCustomerClick={handleCustomerClick}
          onRefresh={refetch}
        />
      )}

      {/* Modals */}
      <AddCustomerModal
        open={showAddCustomer}
        onClose={() => setShowAddCustomer(false)}
        onSuccess={() => {
          setShowAddCustomer(false)
          refetch()
        }}
      />

      {selectedCustomer && (
        <CustomerDetailModal
          customerId={selectedCustomer}
          open={!!selectedCustomer}
          onClose={() => {
            console.log('🚪 Closing modal')
            setSelectedCustomer(null)
          }}
          onUpdate={refetch}
        />
      )}

      {/* Debug info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-black text-white p-2 rounded text-xs z-50">
          Selected Customer: {selectedCustomer || 'None'}
        </div>
      )}
    </div>
  )
}
